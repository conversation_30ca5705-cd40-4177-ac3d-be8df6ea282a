{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?974a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?8cc3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?5eb4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?f968", "uni-app:///pages/welfare.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?f988", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/welfare.vue?7cab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "isReceiving", "methods", "getList", "userId", "console", "res", "uni", "icon", "title", "duration", "receive", "get", "couponId", "haveGet", "mounted", "args", "clearTimeout", "timeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyB12B;EACAC;IACA;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAAA;gBAAA;gBAAA,OAEA;kBACAD;gBACA;cAAA;gBAFAE;gBAGAD;gBACA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBACA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACAL;kBACAC;kBACAC;kBACAC;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;kBACAG;gBACA;cAAA;gBAFAP;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACA;oBACA;sBAAAI;oBAAA;kBACA;kBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAP;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAEA;MAAA;QAAA;MAAA;IAAA;EACA;EACAK;IACA;EACA;AACA,GAEA;AAAA;AACA;EACA;EACA;IAAA;MAAAC;IAAA;IACA;IACAC;IACAC;MAAA;IAAA;EACA;AACA,C;;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/welfare.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/welfare.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./welfare.vue?vue&type=template&id=4f323623&scoped=true&\"\nvar renderjs\nimport script from \"./welfare.vue?vue&type=script&lang=js&\"\nexport * from \"./welfare.vue?vue&type=script&lang=js&\"\nimport style0 from \"./welfare.vue?vue&type=style&index=0&id=4f323623&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f323623\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/welfare.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./welfare.vue?vue&type=template&id=4f323623&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./welfare.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./welfare.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"main_item\" v-for=\"(item,index) in list\" :key=\"index\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<!-- 满减 -->\n\t\t\t\t<view class=\"box1\" v-if=\"item.type == 0\"><span>满</span>{{item.full}}<span>减</span>{{item.discount}}\n\t\t\t\t</view>\n\t\t\t\t<!-- 无门槛 -->\n\t\t\t\t<view class=\"box1\" v-else><span>￥</span>{{item.discount}}</view>\n\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t<text>{{item.title}}</text>\n\t\t\t\t\t<span v-if=\"item.startTime == 0\">有效期：自领券日起{{item.day}}天</span>\n\t\t\t\t\t<span v-else>有效期：{{item.startTime}}至{{item.endTime}}</span>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"box3\" @click=\"receive(item.id,item.haveGet)\"\n\t\t\t\t\t:style=\"item.haveGet == 1?'background-color:#ADADAD;':''\">{{item.haveGet == 1?'已领取':'领取'}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"bottom\">\n\t\t\t\t{{item.rule}}\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tlist: [],\n\t\t\tisReceiving: false // Flag to prevent multiple rapid clicks\n\t\t}\n\t},\n\tmethods: {\n\t\tasync getList() {\n\t\t\tlet userId = uni.getStorageSync('userId');\n\t\t\tconsole.log(userId);\n\t\t\ttry {\n\t\t\t\tlet res = await this.$api.service.getWelfareList({\n\t\t\t\t\tuserId: userId\n\t\t\t\t});\n\t\t\t\tconsole.log(res);\n\t\t\t\tthis.list = res.list;\n\t\t\t\tconsole.log(this.list);\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error(\"获取优惠券列表失败:\", err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\treceive: debounce(async function(id, get) { // Debounced receive function\n\t\t\tif (this.isReceiving) return; // Prevent execution if already processing\n\t\t\tthis.isReceiving = true;\n\t\t\tif (get == 1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '已领取过了',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t\tthis.isReceiving = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tlet res = await this.$api.service.getWelfare({\n\t\t\t\t\tcouponId: [id]\n\t\t\t\t});\n\t\t\t\tif (res.code===\"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '领取成功',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\t// Update local list to reflect claimed status\n\t\t\t\t\tthis.list = this.list.map(item => {\n\t\t\t\t\t\tif (item.id === id) {\n\t\t\t\t\t\t\treturn { ...item, haveGet: 1 };\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn item;\n\t\t\t\t\t});\n\t\t\t\t\t// Refresh coupon list from server\n\t\t\t\t\tawait this.getList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '领取失败',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error(\"领取优惠券失败:\", err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: err,\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.isReceiving = false; // Reset flag after processing\n\t\t\t}\n\t\t}, 1000), // 1000ms debounce delay\n\t},\n\tmounted() {\n\t\tthis.getList();\n\t}\n}\n\n// Debounce function to limit rapid executions\nfunction debounce(func, wait) {\n\tlet timeout;\n\treturn function(...args) {\n\t\tconst context = this;\n\t\tclearTimeout(timeout);\n\t\ttimeout = setTimeout(() => func.apply(context, args), wait);\n\t};\n}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\toverflow: auto;\n\t\tbackground-color: #F8F8F8;\n\t\theight: 100vh;\n\t\tpadding-top: 40rpx;\n\n\t\t.main_item {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: 690rpx;\n\t\t\theight: 202rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.top {\n\t\t\t\theight: 150rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding-top: 26rpx;\n\t\t\t\tpadding-left: 24rpx;\n\t\t\t\tpadding-right: 14rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\n\t\t\t\t.box1 {\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twidth: 180rpx;\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #E72427;\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.box2 {\n\t\t\t\t\tmargin-left: 28rpx;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #171717;\n\t\t\t\t\t\tmax-width: 450rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.box3 {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 24rpx;\n\t\t\t\t\ttop: 24rpx;\n\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\theight: 42rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 22rpx 22rpx 22rpx 22rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tline-height: 42rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bottom {\n\t\t\t\tpadding: 0 24rpx;\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tline-height: 50rpx;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #B2B2B2;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./welfare.vue?vue&type=style&index=0&id=4f323623&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./welfare.vue?vue&type=style&index=0&id=4f323623&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755757260864\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}