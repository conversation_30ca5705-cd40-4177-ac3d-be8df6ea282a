{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?9998", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?018f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?a7a1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?e643", "uni-app:///shifu/creditInfo.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?0bf4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/creditInfo.vue?3020"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "loadingMore", "currentCredit", "typeIndex", "reasonIndex", "typeOptions", "label", "value", "reasonOptions", "creditRecords", "pageNum", "pageSize", "totalCount", "totalPage", "hasMore", "onLoad", "console", "methods", "loadCurrentCredit", "selectType", "selectReason", "loadCreditInfo", "isLoadMore", "currentRecords", "params", "response", "newRecords", "totalRecords", "uni", "title", "icon", "loadMore", "handleSearch", "resetFilters", "getCreditLevel", "getReasonText", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8F72B;EACAC;IACA;MACA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,gBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;MACA;MACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;UACA;UACA;UACAF;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACAN;kBAAAM;kBAAAC;gBAAA;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAP;gBACA;kBACA;kBACAA;gBACA;gBAAA;gBAGA;gBACAQ;kBACAd;kBACAC;gBACA,GAEA;gBACA;kBACAa;gBACA;gBACA;kBACAA;gBACA;gBAEAR;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAS;gBACAT;gBAAA,MAEAS;kBAAA;kBAAA;gBAAA;gBACA1B,sBAEA;gBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA2B;gBAEA;kBACA;kBACAV;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAEAA;kBACAM;kBACAZ;kBACAgB;kBACAC;kBACAf;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBACAY;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACAhB;MACA;IACA;IAEA;IACAiB;MACA;MACA;MACAjB;MACA;MACA;IACA;IAEA;IACAkB;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/creditInfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/creditInfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./creditInfo.vue?vue&type=template&id=893dab3e&scoped=true&\"\nvar renderjs\nimport script from \"./creditInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./creditInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./creditInfo.vue?vue&type=style&index=0&id=893dab3e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"893dab3e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/creditInfo.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./creditInfo.vue?vue&type=template&id=893dab3e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.loading ? _vm.getCreditLevel(_vm.currentCredit) : null\n  var g0 = !_vm.loading ? _vm.creditRecords.length : null\n  var l0 =\n    !_vm.loading && !(g0 === 0)\n      ? _vm.__map(_vm.creditRecords, function (record, index) {\n          var $orig = _vm.__get_orig(record)\n          var m1 = _vm.formatDate(record.createTime)\n          var m2 = _vm.getReasonText(record.changeReason)\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./creditInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./creditInfo.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page-container\">\n\t\t<!-- 顶部导航 -->\n\t\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t<view class=\"loading-text\">加载中...</view>\n\t\t</view>\n\n\t\t<!-- 信用信息内容 -->\n\t\t<view v-else class=\"credit-content\">\n\t\t\t<!-- 信用评分卡片 -->\n\t\t\t<view class=\"credit-card\">\n\t\t\t\t<view class=\"credit-header\">\n\t\t\t\t\t<view class=\"credit-title\">当前信用分</view>\n\t\t\t\t\t<view class=\"credit-score\">{{ currentCredit }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"credit-level\">{{ getCreditLevel(currentCredit) }}</view>\n\t\t\t</view>\n\n\t\t\t<!-- 筛选条件 -->\n\t\t\t<view class=\"filter-section\">\n\t\t\t\t<view class=\"filter-title\">筛选条件</view>\n\t\t\t\t<view class=\"filter-options\">\n\t\t\t\t\t<view class=\"filter-item\">\n\t\t\t\t\t\t<view class=\"filter-label\">信誉分类型:</view>\n\t\t\t\t\t\t<view class=\"filter-dropdown\">\n\t\t\t\t\t\t\t<view class=\"dropdown-item\"\n\t\t\t\t\t\t\t\tv-for=\"(option, index) in typeOptions\"\n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"{ active: typeIndex === index }\"\n\t\t\t\t\t\t\t\t@click=\"selectType(index)\">\n\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"filter-item\">\n\t\t\t\t\t\t<view class=\"filter-label\">变动原因:</view>\n\t\t\t\t\t\t<view class=\"filter-dropdown\">\n\t\t\t\t\t\t\t<view class=\"dropdown-item\"\n\t\t\t\t\t\t\t\tv-for=\"(option, index) in reasonOptions\"\n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"{ active: reasonIndex === index }\"\n\t\t\t\t\t\t\t\t@click=\"selectReason(index)\">\n\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t<button @click=\"resetFilters\" class=\"reset-btn\">重置</button>\n\t\t\t\t\t<button @click=\"handleSearch\" class=\"search-btn\">查询</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 信用变动记录 -->\n\t\t\t<view class=\"credit-records\">\n\t\t\t\t<view class=\"records-title\">信用变动记录</view>\n\t\t\t\t<view v-if=\"creditRecords.length === 0\" class=\"no-data\">\n\t\t\t\t\t<view class=\"no-data-text\">暂无记录</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"records-list\">\n\t\t\t\t\t<view class=\"record-item\" v-for=\"(record, index) in creditRecords\" :key=\"record.id\">\n\t\t\t\t\t\t<view class=\"record-header\">\n\t\t\t\t\t\t\t<view class=\"record-type\" :class=\"record.creditType === 1 ? 'add' : 'minus'\">\n\t\t\t\t\t\t\t\t{{ record.creditType === 1 ? '+' : '-' }}{{ record.points }}分\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"record-date\">{{ formatDate(record.createTime) }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"record-content\">\n\t\t\t\t\t\t\t<view class=\"record-reason\">{{ getReasonText(record.changeReason) }}</view>\n\t\t\t\t\t\t\t<view class=\"record-detail\">\n\t\t\t\t\t\t\t\t<text>变动前: {{ record.beforePoints }}分</text>\n\t\t\t\t\t\t\t\t<text>变动后: {{ record.afterPoints }}分</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-if=\"record.remark\" class=\"record-remark\">备注: {{ record.remark }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 分页信息 -->\n\t\t\t\t<view v-if=\"totalCount > 0\" class=\"pagination-info\">\n\t\t\t\t\t<text>共{{ totalCount }}条记录，第{{ pageNum }}/{{ totalPage }}页</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 加载更多 -->\n\t\t\t\t<view v-if=\"hasMore\" class=\"load-more\" @click=\"loadMore\">\n\t\t\t\t\t<view class=\"load-more-text\">{{ loadingMore ? '加载中...' : '加载更多' }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t// 加载状态\n\t\t\tloading: false,\n\t\t\tloadingMore: false,\n\n\t\t\t// 当前信用分\n\t\t\tcurrentCredit: 0,\n\n\t\t\t// 筛选条件\n\t\t\ttypeIndex: 0,\n\t\t\treasonIndex: 0,\n\t\t\ttypeOptions: [\n\t\t\t\t{ label: '全部', value: null },\n\t\t\t\t{ label: '加分', value: 1 },\n\t\t\t\t{ label: '扣分', value: 2 }\n\t\t\t],\n\t\t\treasonOptions: [\n\t\t\t\t{ label: '全部', value: null },\n\t\t\t\t{ label: '完成订单', value: 1 },\n\t\t\t\t{ label: '客户好评', value: 2 },\n\t\t\t\t{ label: '客户差评', value: 3 },\n\t\t\t\t// { label: '投诉', value: 4 },\n\t\t\t\t{ label: '违规', value: 5 },\n\t\t\t\t{ label: '取消订单', value: 6 }\n\t\t\t],\n\n\t\t\t// 信用记录数据\n\t\t\tcreditRecords: [],\n\t\t\tpageNum: 1,\n\t\t\tpageSize: 10,\n\t\t\ttotalCount: 0,\n\t\t\ttotalPage: 0,\n\t\t\thasMore: false\n\t\t}\n\t},\n\tonLoad() {\n\t\t// 页面加载时获取当前信用分\n\t\tthis.loadCurrentCredit();\n\t\t// 获取信用信息 - 首次加载\n\t\tconsole.log('页面加载，首次获取信用信息');\n\t\tthis.loadCreditInfo(false);\n\t},\n\tmethods: {\n\t\t// 加载当前信用分\n\t\tloadCurrentCredit() {\n\t\t\ttry {\n\t\t\t\t// 从本地存储获取师傅信息\n\t\t\t\tconst shiInfo = uni.getStorageSync('shiInfo');\n\t\t\t\tif (shiInfo) {\n\t\t\t\t\tconst parsedShiInfo = JSON.parse(shiInfo);\n\t\t\t\t\tthis.currentCredit = parsedShiInfo.credit || 0;\n\t\t\t\t\tconsole.log('从本地存储获取当前信用分:', this.currentCredit);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取当前信用分失败:', error);\n\t\t\t\tthis.currentCredit = 0;\n\t\t\t}\n\t\t},\n\n\t\t// 选择信誉分类型\n\t\tselectType(index) {\n\t\t\tthis.typeIndex = index;\n\t\t\t// 选择后自动重新查询\n\t\t\tthis.loadCreditInfo(false);\n\t\t},\n\n\t\t// 选择变动原因\n\t\tselectReason(index) {\n\t\t\tthis.reasonIndex = index;\n\t\t\t// 选择后自动重新查询\n\t\t\tthis.loadCreditInfo(false);\n\t\t},\n\n\t\t// 加载信用信息\n\t\tasync loadCreditInfo(isLoadMore = false) {\n\t\t\tconsole.log('loadCreditInfo调用:', { isLoadMore, currentRecords: this.creditRecords.length });\n\n\t\t\tif (!isLoadMore) {\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.pageNum = 1;\n\t\t\t\t// 重新查询时必须清空之前的数据\n\t\t\t\tthis.creditRecords = [];\n\t\t\t\tthis.totalCount = 0;\n\t\t\t\tthis.totalPage = 0;\n\t\t\t\tthis.hasMore = false;\n\t\t\t\tconsole.log('清空数据，重新查询');\n\t\t\t} else {\n\t\t\t\tthis.loadingMore = true;\n\t\t\t\tconsole.log('加载更多，当前页码:', this.pageNum);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t\t// 构建请求参数\n\t\t\t\t\tconst params = {\n\t\t\t\t\t\tpageNum: this.pageNum,\n\t\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t\t};\n\n\t\t\t\t\t// 添加筛选条件\n\t\t\t\t\tif (this.typeOptions[this.typeIndex].value !== null) {\n\t\t\t\t\t\tparams.type = this.typeOptions[this.typeIndex].value;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.reasonOptions[this.reasonIndex].value !== null) {\n\t\t\t\t\t\tparams.reason = this.reasonOptions[this.reasonIndex].value;\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('请求参数:', params);\n\n\t\t\t\t\t// 调用API\n\t\t\t\t\tconst response = await this.$api.shifu.getcreditInfo(params);\n\t\t\t\t\tconsole.log('API响应:', response);\n\n\t\t\t\t\tif (response && response.code === '200' && response.data) {\n\t\t\t\t\t\tconst data = response.data;\n\n\t\t\t\t\t\t// 更新当前信用分（从第一条记录获取，或者从师傅信息获取）\n\t\t\t\t\t\tif (data.list && data.list.length > 0 && !isLoadMore) {\n\t\t\t\t\t\t\t// 只在首次加载时更新当前信用分\n\t\t\t\t\t\t\tthis.currentCredit = data.list[0].afterPoints || this.currentCredit;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 处理记录列表 - 确保不会累加错误数据\n\t\t\t\t\t\tconst newRecords = data.list || [];\n\n\t\t\t\t\t\tif (isLoadMore) {\n\t\t\t\t\t\t\t// 加载更多时，追加新数据\n\t\t\t\t\t\t\tconsole.log('追加新数据:', newRecords.length, '条');\n\t\t\t\t\t\t\tthis.creditRecords = [...this.creditRecords, ...newRecords];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 首次加载或重新查询时，完全替换数据\n\t\t\t\t\t\t\tconsole.log('替换所有数据:', newRecords.length, '条');\n\t\t\t\t\t\t\tthis.creditRecords = [...newRecords]; // 使用展开运算符确保是新数组\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 更新分页信息\n\t\t\t\t\t\tthis.totalCount = data.totalCount || 0;\n\t\t\t\t\t\tthis.totalPage = data.totalPage || 0;\n\t\t\t\t\t\tthis.hasMore = this.pageNum < this.totalPage;\n\n\t\t\t\t\t\tconsole.log('信用记录加载成功:', {\n\t\t\t\t\t\t\tisLoadMore,\n\t\t\t\t\t\t\tpageNum: this.pageNum,\n\t\t\t\t\t\t\tnewRecords: newRecords.length,\n\t\t\t\t\t\t\ttotalRecords: this.creditRecords.length,\n\t\t\t\t\t\t\ttotalCount: this.totalCount,\n\t\t\t\t\t\t\ttotalPage: this.totalPage,\n\t\t\t\t\t\t\thasMore: this.hasMore\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.msg || '获取信用信息失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取信用信息失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '获取信用信息失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.loadingMore = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载更多\n\t\t\tloadMore() {\n\t\t\t\tif (this.hasMore && !this.loadingMore) {\n\t\t\t\t\tthis.pageNum++;\n\t\t\t\t\tthis.loadCreditInfo(true);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 查询按钮点击\n\t\t\thandleSearch() {\n\t\t\t\tconsole.log('手动查询，清空数据重新加载');\n\t\t\t\tthis.loadCreditInfo(false);\n\t\t\t},\n\n\t\t\t// 重置筛选条件\n\t\t\tresetFilters() {\n\t\t\t\tthis.typeIndex = 0;\n\t\t\t\tthis.reasonIndex = 0;\n\t\t\t\tconsole.log('重置筛选条件，清空数据重新加载');\n\t\t\t\t// 重置后重新查询，确保清空之前的数据\n\t\t\t\tthis.loadCreditInfo(false);\n\t\t\t},\n\n\t\t\t// 获取信用等级\n\t\t\tgetCreditLevel(score) {\n\t\t\t\tif (score >= 90) return '优秀师傅';\n\t\t\t\tif (score >= 80) return '良好师傅';\n\t\t\t\tif (score >= 70) return '合格师傅';\n\t\t\t\tif (score >= 60) return '待改进师傅';\n\t\t\t\treturn '需要提升';\n\t\t\t},\n\n\t\t\t// 获取变动原因文本\n\t\t\tgetReasonText(reason) {\n\t\t\t\tconst reasonMap = {\n\t\t\t\t\t1: '完成订单',\n\t\t\t\t\t2: '客户好评',\n\t\t\t\t\t3: '客户差评',\n\t\t\t\t\t4: '投诉',\n\t\t\t\t\t5: '违规',\n\t\t\t\t\t6: '取消订单'\n\t\t\t\t};\n\t\t\t\treturn reasonMap[reason] || '未知原因';\n\t\t\t},\n\n\t\t\t// 格式化日期\n\t\t\tformatDate(dateStr) {\n\t\t\t\tif (!dateStr) return '';\n\t\t\t\tconst date = new Date(dateStr);\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}`;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.page-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t.header {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 20rpx 30rpx;\n\t\tcolor: white;\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 100;\n\t}\n\n\t.header-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\ttext-align: center;\n\t}\n\n\t.loading-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 400rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.credit-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t.credit-card {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #599EFF 100%);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tcolor: white;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.credit-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.credit-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.credit-score {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.credit-level {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t}\n\n\t.filter-section {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.filter-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 20rpx;\n\t\tcolor: #333;\n\t}\n\n\t.filter-options {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.filter-item {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.filter-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 15rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.filter-dropdown {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 15rpx;\n\t}\n\n\t.dropdown-item {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tpadding: 15rpx 25rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 25rpx;\n\t\tborder: 2rpx solid #e9ecef;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tbackground: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);\n\t\t\tcolor: white;\n\t\t\tborder-color: #599EFF;\n\t\t\ttransform: scale(1.05);\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground: #e9ecef;\n\t\t}\n\n\t\t&.active:hover {\n\t\t\tbackground: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);\n\t\t}\n\t}\n\n\t.filter-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.reset-btn, .search-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tborder: none;\n\t}\n\n\t.reset-btn {\n\t\tbackground: #f8f9fa;\n\t\tcolor: #666;\n\t}\n\n\t.search-btn {\n\t\tbackground: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);\n\t\tcolor: white;\n\t}\n\n\t.credit-records {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.records-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 20rpx;\n\t\tcolor: #333;\n\t}\n\n\t.no-data {\n\t\ttext-align: center;\n\t\tpadding: 80rpx 0;\n\t}\n\n\t.no-data-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.records-list {\n\t\t.record-item {\n\t\t\tborder-bottom: 1px solid #f0f0f0;\n\t\t\tpadding: 20rpx 0;\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t.record-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.record-type {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\n\t\t&.add {\n\t\t\tcolor: #52c41a;\n\t\t}\n\n\t\t&.minus {\n\t\t\tcolor: #ff4d4f;\n\t\t}\n\t}\n\n\t.record-date {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.record-content {\n\t\tpadding-left: 20rpx;\n\t}\n\n\t.record-reason {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.record-detail {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 10rpx;\n\n\t\ttext {\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\t}\n\n\t.record-remark {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tfont-style: italic;\n\t}\n\n\t.pagination-info {\n\t\ttext-align: center;\n\t\tpadding: 20rpx 0;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.load-more {\n\t\ttext-align: center;\n\t\tpadding: 30rpx 0;\n\t\tcursor: pointer;\n\t}\n\n\t.load-more-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #667eea;\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./creditInfo.vue?vue&type=style&index=0&id=893dab3e&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./creditInfo.vue?vue&type=style&index=0&id=893dab3e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755757257021\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}